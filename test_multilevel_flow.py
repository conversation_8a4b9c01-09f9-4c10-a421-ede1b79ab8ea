#!/usr/bin/env python3
"""
Test script to demonstrate the new multilevel flow chart functionality.
This shows how multilevel flows are composed from two-level operations.
"""

import os
import tempfile
from mobility.serializers.charters.flow_chart_grapher import FlowChartGrapher

def test_multilevel_flow_chart():
    """Test the new multilevel flow chart functionality."""
    
    # Create temporary output directory
    with tempfile.TemporaryDirectory() as temp_dir:
        
        # Example 1: 3-level budget flow (Income → Budget → Categories)
        flows_3_level = [
            ("Wages", "Budget", "Housing"),
            ("Wages", "Budget", "Housing"),
            ("Wages", "Budget", "Housing"),
            ("Wages", "Budget", "Food"),
            ("Wages", "Budget", "Food"),
            ("Other", "Budget", "Transportation"),
            ("Other", "Budget", "Savings"),
        ]
        
        categories_order = ["Wages", "Other", "Budget", "Housing", "Food", "Transportation", "Savings"]
        colors = {
            "Wages": "#4CAF50",
            "Other": "#2196F3", 
            "Budget": "#FF9800",
            "Housing": "#F44336",
            "Food": "#9C27B0",
            "Transportation": "#00BCD4",
            "Savings": "#8BC34A"
        }
        categories_labels = {cat: cat for cat in categories_order}
        categories_icons = {cat: "circle" for cat in categories_order}
        
        grapher = FlowChartGrapher[str](temp_dir)
        
        # Generate 3-level chart
        output_file = grapher.make_multilevel_chart(
            file_name="budget_flow_3_level",
            title="Budget Flow Analysis (3 Levels)",
            flows=flows_3_level,
            level_titles=["Income", "Budget", "Categories"],
            colors=colors,
            categories_order=categories_order,
            categories_labels=categories_labels,
            categories_icons=categories_icons,
        )
        
        print(f"3-level chart generated: {output_file}")
        
        # Example 2: 4-level geographic flow (Department → Region → City → District)
        flows_4_level = [
            ("Dept_A", "Region_1", "City_X", "District_1"),
            ("Dept_A", "Region_1", "City_X", "District_2"),
            ("Dept_A", "Region_1", "City_Y", "District_3"),
            ("Dept_B", "Region_2", "City_Z", "District_4"),
            ("Dept_B", "Region_2", "City_Z", "District_5"),
        ]
        
        categories_order_4 = ["Dept_A", "Dept_B", "Region_1", "Region_2", "City_X", "City_Y", "City_Z", 
                             "District_1", "District_2", "District_3", "District_4", "District_5"]
        colors_4 = {cat: "#607D8B" for cat in categories_order_4}  # Simple gray color scheme
        categories_labels_4 = {cat: cat for cat in categories_order_4}
        categories_icons_4 = {cat: "circle" for cat in categories_order_4}
        
        # Generate 4-level chart
        output_file_4 = grapher.make_multilevel_chart(
            file_name="geographic_flow_4_level",
            title="Geographic Flow Analysis (4 Levels)",
            flows=flows_4_level,
            level_titles=["Department", "Region", "City", "District"],
            colors=colors_4,
            categories_order=categories_order_4,
            categories_labels=categories_labels_4,
            categories_icons=categories_icons_4,
        )
        
        print(f"4-level chart generated: {output_file_4}")
        
        # Example 3: Compare with traditional 2-level chart
        flows_2_level = [("Wages", "Housing"), ("Wages", "Food"), ("Other", "Transportation"), ("Other", "Savings")]
        
        output_file_2 = grapher.make_chart(
            file_name="traditional_2_level",
            title="Traditional 2-Level Flow Chart",
            flows=flows_2_level,
            titles=("Income", "Categories"),
            colors=colors,
            categories_order=categories_order,
            categories_labels=categories_labels,
            categories_icons=categories_icons,
        )
        
        print(f"2-level chart generated: {output_file_2}")
        print(f"\nAll charts saved to: {temp_dir}")
        print("Implementation successfully demonstrates composition-based multilevel functionality!")

if __name__ == "__main__":
    test_multilevel_flow_chart()

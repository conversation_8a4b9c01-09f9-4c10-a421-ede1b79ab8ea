#!/usr/bin/env python3
"""
4-Level Aggregation/Disaggregation Sankey Diagram for Strasbourg Mobility Flows
Demonstrates hierarchical flow visualization with aggregation→disaggregation pattern.
"""

import os
import pandas as pd
from mobility.serializers.charters.flow_chart_grapher import FlowChartGrapher


def process_csv_to_4_level_flows(df):
    """
    Transform CSV data into 4-level aggregation/disaggregation flows.

    4-Level Structure:
    1. Origins: Strasbourg communes only
    2. Origin Aggregation: Strasbourg → "Strasbourg", others unchanged
    3. Destination Aggregation: Same as level 2
    4. Destinations: Strasbourg communes only
    """
    flow_data = []

    for _, row in df.iterrows():
        origin = row["origin_zone"]
        destination = row["destination_zone"]
        count = int(row["count"])

        # Skip if count is 0
        if count == 0:
            continue

        # Check if origin/destination are Strasbourg communes
        is_origin_strasbourg = origin.startswith("Strasbourg")
        is_dest_strasbourg = destination.startswith("Strasbourg")

        # Only process flows involving at least one Strasbourg commune
        if not (is_origin_strasbourg or is_dest_strasbourg):
            continue

        # Create 4-level flow tuples based on origin/destination types
        if is_origin_strasbourg and is_dest_strasbourg:
            # Strasbourg to Strasbourg: commune → dept → dept → commune
            flow_tuple = (origin, "Strasbourg", "Strasbourg", destination, count)
        elif is_origin_strasbourg and not is_dest_strasbourg:
            # Strasbourg to external: commune → dept → external → external
            flow_tuple = (origin, "Strasbourg", destination, destination, count)
        elif not is_origin_strasbourg and is_dest_strasbourg:
            # External to Strasbourg: external → external → dept → commune
            flow_tuple = (origin, origin, "Strasbourg", destination, count)
        else:
            # This case is already filtered out above
            continue

        flow_data.append(flow_tuple)

    return flow_data


def create_strasbourg_aggregation_flow():
    """Create 4-level aggregation/disaggregation flow chart for Strasbourg mobility."""

    # CSV file path
    csv_path = r"C:\Users\<USER>\Documents\affaires\23I711.3 Strasbourg OD PL FCD\spatial_analysis_output\aggregated\hv_aggregated_od_matrix_total_RN4_Est_Ouest.csv"

    # Output directory
    output_dir = r"C:\Users\<USER>\Documents\affaires\23I711.3 Strasbourg OD PL FCD\spatial_analysis_output\flow_charts"
    os.makedirs(output_dir, exist_ok=True)

    # Read CSV data
    print(f"Reading CSV data from: {csv_path}")
    df = pd.read_csv(csv_path)
    print(f"Loaded {len(df)} rows of flow data")

    # Process CSV data into 4-level structure
    flow_data = process_csv_to_4_level_flows(df)
    print(f"Generated {len(flow_data)} 4-level flow records")

    # Extract unique categories from the processed flow data
    all_categories = set()
    for level1, level2, level3, level4, count in flow_data:
        all_categories.update([level1, level2, level3, level4])

    # Expand to individual flow tuples
    flows_4_level = []
    for level1, level2, level3, level4, count in flow_data:
        flows_4_level.extend([(level1, level2, level3, level4)] * count)

    # Create categories order from the data
    strasbourg_communes = sorted(
        [
            cat
            for cat in all_categories
            if cat.startswith("Strasbourg") and cat != "Strasbourg"
        ]
    )
    external_zones = sorted(
        [cat for cat in all_categories if not cat.startswith("Strasbourg")]
    )

    categories_order = strasbourg_communes + ["Strasbourg"] + external_zones
    print(f"Categories found: {len(categories_order)} total")
    print(f"Strasbourg communes: {len(strasbourg_communes)}")
    print(f"External zones: {len(external_zones)}")

    # Generate dynamic colors and labels
    colors = {}
    categories_labels = {}
    categories_icons = {}

    # Color schemes
    strasbourg_colors = [
        "#E53935",
        "#1E88E5",
        "#43A047",
        "#9C27B0",
        "#FF5722",
        "#795548",
        "#607D8B",
    ]
    external_colors = [
        "#4CAF50",
        "#2196F3",
        "#FF9800",
        "#9C27B0",
        "#00BCD4",
        "#8BC34A",
        "#FFC107",
    ]

    # Assign colors and labels
    for i, commune in enumerate(strasbourg_communes):
        colors[commune] = strasbourg_colors[i % len(strasbourg_colors)]
        # Simplify long Strasbourg commune names
        if " - " in commune:
            categories_labels[commune] = commune.split(" - ", 1)[1]
        else:
            categories_labels[commune] = commune.replace("Strasbourg ", "")
        categories_icons[commune] = "car"

    # Strasbourg department
    colors["Strasbourg"] = "#FF9800"
    categories_labels["Strasbourg"] = "Strasbourg"
    categories_icons["Strasbourg"] = "region"

    # External zones
    for i, zone in enumerate(external_zones):
        colors[zone] = external_colors[i % len(external_colors)]
        # Simplify long external zone names
        if len(zone) > 15:
            categories_labels[zone] = zone[:12] + "..."
        else:
            categories_labels[zone] = zone
        categories_icons[zone] = "location"

    # Level titles explaining the aggregation/disaggregation pattern
    level_titles = [
        "Origins (Mixed)",  # Level 1: Communes + Departments
        "Origin Departments",  # Level 2: Aggregated origins
        "Destination Departments",  # Level 3: Aggregated destinations
        "Destinations (Mixed)",  # Level 4: Communes + Departments
    ]

    grapher = FlowChartGrapher[str](output_dir)

    # Generate the aggregation/disaggregation flow chart
    output_file = grapher.make_multilevel_chart(
        file_name="strasbourg_aggregation_disaggregation",
        title="Strasbourg Mobility: Aggregation/Disaggregation Flow Analysis",
        flows=flows_4_level,
        level_titles=level_titles,
        colors=colors,
        categories_order=categories_order,
        categories_labels=categories_labels,
        categories_icons=categories_icons,
        level_spacing=350,  # Wider spacing for complex labels
    )

    print(f"Aggregation/Disaggregation flow chart generated: {output_file}")
    print("\nFlow Pattern Explanation:")
    print("Level 1→2: Strasbourg communes aggregate to department level")
    print("Level 2→3: Department-to-department flows (lateral)")
    print("Level 3→4: Strasbourg department disaggregates to communes")
    print("External departments remain aggregated throughout")

    return output_file


if __name__ == "__main__":
    create_strasbourg_aggregation_flow()

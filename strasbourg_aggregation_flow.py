#!/usr/bin/env python3
"""
4-Level Aggregation/Disaggregation Sankey Diagram for Strasbourg Mobility Flows
Demonstrates hierarchical flow visualization with aggregation→disaggregation pattern.
"""

import os
from mobility.serializers.charters.flow_chart_grapher import FlowChartGrapher

def create_strasbourg_aggregation_flow():
    """Create 4-level aggregation/disaggregation flow chart for Strasbourg mobility."""

    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)

    # Sample flow data with counts (Origin_Commune, Origin_Dept, Dest_Dept, Dest_Commune, Count)
    flow_data = [
        # Strasbourg internal flows (commune → dept → dept → commune)
        ("Strasbourg - Gare", "Strasbourg Department", "Strasbourg Department", "Strasbourg - Centre", 25),
        ("Strasbourg - Centre", "Strasbourg Department", "Strasbourg Department", "Strasbourg - Gare", 20),
        ("Strasbourg - Krutenau", "Strasbourg Department", "Strasbourg Department", "Strasbourg - Centre", 15),
        ("Strasbourg - Centre", "Strasbourg Department", "Strasbourg Department", "Strasbourg - Krutenau", 18),
        ("Strasbourg - Gare", "Strasbourg Department", "Strasbourg Department", "Strasbourg - Krutenau", 12),

        # External to Strasbourg flows (dept → dept → dept → commune)
        ("EMS_1ere couronne sud", "EMS_1ere couronne sud", "Strasbourg Department", "Strasbourg - Gare", 30),
        ("EMS_1ere couronne sud", "EMS_1ere couronne sud", "Strasbourg Department", "Strasbourg - Centre", 22),
        ("EMS_1ere couronne sud", "EMS_1ere couronne sud", "Strasbourg Department", "Strasbourg - Krutenau", 18),
        ("EMS_Couronne Nord", "EMS_Couronne Nord", "Strasbourg Department", "Strasbourg - Gare", 15),
        ("EMS_Couronne Nord", "EMS_Couronne Nord", "Strasbourg Department", "Strasbourg - Centre", 12),
        ("BMNord_Sud Ouest (Piémont, Bruche)", "BMNord_Sud Ouest (Piémont, Bruche)", "Strasbourg Department", "Strasbourg - Centre", 8),

        # Strasbourg to external flows (commune → dept → dept → dept)
        ("Strasbourg - Gare", "Strasbourg Department", "EMS_1ere couronne sud", "EMS_1ere couronne sud", 28),
        ("Strasbourg - Centre", "Strasbourg Department", "EMS_1ere couronne sud", "EMS_1ere couronne sud", 24),
        ("Strasbourg - Krutenau", "Strasbourg Department", "EMS_1ere couronne sud", "EMS_1ere couronne sud", 16),
        ("Strasbourg - Gare", "Strasbourg Department", "EMS_Couronne Nord", "EMS_Couronne Nord", 14),
        ("Strasbourg - Centre", "Strasbourg Department", "EMS_Couronne Nord", "EMS_Couronne Nord", 10),
        ("Strasbourg - Centre", "Strasbourg Department", "BMNord_Sud Ouest (Piémont, Bruche)", "BMNord_Sud Ouest (Piémont, Bruche)", 6),
    ]

    # Expand to individual flow tuples
    flows_4_level = []
    for level1, level2, level3, level4, count in flow_data:
        flows_4_level.extend([(level1, level2, level3, level4)] * count)

    # Categories order - organize by hierarchy level
    categories_order = [
        # Level 1: Origins (communes + departments)
        "Strasbourg - Gare", "Strasbourg - Centre", "Strasbourg - Krutenau",
        "EMS_1ere couronne sud", "EMS_Couronne Nord", "BMNord_Sud Ouest (Piémont, Bruche)",

        # Level 2 & 3: Departments (aggregation level)
        "Strasbourg Department",

        # Level 4: Destinations (communes + departments)
        # Note: Some overlap with Level 1 is expected and handled automatically
    ]

    # Remove duplicates while preserving order
    seen = set()
    categories_order = [x for x in categories_order if not (x in seen or seen.add(x))]

    # Color scheme: differentiate by geographic type and flow direction
    colors = {
        # Strasbourg communes (detailed level)
        "Strasbourg - Gare": "#E53935",        # Red for train station
        "Strasbourg - Centre": "#1E88E5",      # Blue for city center
        "Strasbourg - Krutenau": "#43A047",    # Green for residential area

        # Departments (aggregation level)
        "Strasbourg Department": "#FF9800",    # Orange for Strasbourg dept
        "EMS_1ere couronne sud": "#9C27B0",                 # Purple for EMS_1ere couronne sud
        "EMS_Couronne Nord": "#00ACC1",                # Teal for EMS_Couronne Nord
        "BMNord_Sud Ouest (Piémont, Bruche)": "#8BC34A",                  # Light green for BMNord_Sud Ouest (Piémont, Bruche)
    }

    # Simplified labels for better readability
    categories_labels = {
        "Strasbourg - Gare": "Gare",
        "Strasbourg - Centre": "Centre",
        "Strasbourg - Krutenau": "Krutenau",
        "Strasbourg Department": "Strasbourg",
        "EMS_1ere couronne sud": "EMS_1ere couronne sud",
        "EMS_Couronne Nord": "EMS_Couronne Nord",
        "BMNord_Sud Ouest (Piémont, Bruche)": "BMNord_Sud Ouest (Piémont, Bruche)",
    }

    # Geographic icons
    categories_icons = {
        "Strasbourg - Gare": "car",
        "Strasbourg - Centre": "car",
        "Strasbourg - Krutenau": "car",
        "Strasbourg Department": "car",
        "EMS_1ere couronne sud": "car",
        "EMS_Couronne Nord": "car",
        "BMNord_Sud Ouest (Piémont, Bruche)": "car",
    }

    # Level titles explaining the aggregation/disaggregation pattern
    level_titles = [
        "Origins (Mixed)",           # Level 1: Communes + Departments
        "Origin Departments",        # Level 2: Aggregated origins
        "Destination Departments",   # Level 3: Aggregated destinations
        "Destinations (Mixed)"       # Level 4: Communes + Departments
    ]

    grapher = FlowChartGrapher[str](output_dir)

    # Generate the aggregation/disaggregation flow chart
    output_file = grapher.make_multilevel_chart(
        file_name="strasbourg_aggregation_disaggregation",
        title="Strasbourg Mobility: Aggregation/Disaggregation Flow Analysis",
        flows=flows_4_level,
        level_titles=level_titles,
        colors=colors,
        categories_order=categories_order,
        categories_labels=categories_labels,
        categories_icons=categories_icons,
        level_spacing=350,  # Wider spacing for complex labels
    )

    print(f"Aggregation/Disaggregation flow chart generated: {output_file}")
    print("\nFlow Pattern Explanation:")
    print("Level 1→2: Strasbourg communes aggregate to department level")
    print("Level 2→3: Department-to-department flows (lateral)")
    print("Level 3→4: Strasbourg department disaggregates to communes")
    print("External departments remain aggregated throughout")

    return output_file

def create_csv_version():
    """Create CSV version for easier data management."""

    # Create sample CSV data
    csv_data = """origin,origin_dept,dest_dept,destination,count
Strasbourg - Gare,Strasbourg Department,Strasbourg Department,Strasbourg - Centre,25
Strasbourg - Centre,Strasbourg Department,Strasbourg Department,Strasbourg - Gare,20
Strasbourg - Krutenau,Strasbourg Department,Strasbourg Department,Strasbourg - Centre,15
EMS_1ere couronne sud,EMS_1ere couronne sud,Strasbourg Department,Strasbourg - Gare,30
EMS_1ere couronne sud,EMS_1ere couronne sud,Strasbourg Department,Strasbourg - Centre,22
EMS_Couronne Nord,EMS_Couronne Nord,Strasbourg Department,Strasbourg - Gare,15
Strasbourg - Gare,Strasbourg Department,EMS_1ere couronne sud,EMS_1ere couronne sud,28
Strasbourg - Centre,Strasbourg Department,EMS_Couronne Nord,EMS_Couronne Nord,10"""

    # Save CSV file
    with open("strasbourg_flows.csv", "w") as f:
        f.write(csv_data)

    print("CSV file 'strasbourg_flows.csv' created")
    print("Use create_multilevel_flow_chart_from_csv() with level_columns:")
    print("['origin', 'origin_dept', 'dest_dept', 'destination']")

if __name__ == "__main__":
    create_strasbourg_aggregation_flow()
    create_csv_version()

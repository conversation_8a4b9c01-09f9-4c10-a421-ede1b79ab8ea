#!/usr/bin/env python3
"""
Test script to verify CSV processing logic for Strasbourg multilevel flows.
"""

import pandas as pd
from strasbourg_aggregation_flow import process_csv_to_4_level_flows

def test_csv_processing():
    """Test the CSV processing function with sample data."""
    
    # Create sample CSV data similar to the real structure
    sample_data = {
        'origin_zone': [
            'Strasbourg - Centre',
            'Strasbourg - Gare', 
            'Strasbourg - Krutenau',
            'EMS_1ere couronne sud',
            'EMS_Couronne Nord',
            'BMNord_Sud Ouest (Piémont, Bruche)',
            'Strasbourg - Centre',
            'EMS_1ere couronne sud',
        ],
        'destination_zone': [
            'Strasbourg - Gare',
            'Strasbourg - Centre',
            'EMS_1ere couronne sud', 
            'Strasbourg - Centre',
            'Strasbourg - Gare',
            'Strasbourg - Krutenau',
            'EMS_Couronne Nord',
            'BMNord_Sud Ouest (Piémont, Bruche)',
        ],
        'count': [25, 20, 15, 30, 22, 18, 14, 8]
    }
    
    df = pd.DataFrame(sample_data)
    print("Sample CSV data:")
    print(df)
    print()
    
    # Process the data
    flow_data = process_csv_to_4_level_flows(df)
    
    print(f"Generated {len(flow_data)} 4-level flow records:")
    for i, (level1, level2, level3, level4, count) in enumerate(flow_data):
        print(f"{i+1:2d}. {level1:25} → {level2:15} → {level3:15} → {level4:25} ({count:2d})")
    
    print()
    
    # Analyze the flow patterns
    strasbourg_internal = [f for f in flow_data if f[0].startswith('Strasbourg') and f[3].startswith('Strasbourg')]
    external_to_strasbourg = [f for f in flow_data if not f[0].startswith('Strasbourg') and f[3].startswith('Strasbourg')]
    strasbourg_to_external = [f for f in flow_data if f[0].startswith('Strasbourg') and not f[3].startswith('Strasbourg')]
    
    print("Flow Pattern Analysis:")
    print(f"Strasbourg internal flows: {len(strasbourg_internal)}")
    print(f"External → Strasbourg flows: {len(external_to_strasbourg)}")
    print(f"Strasbourg → External flows: {len(strasbourg_to_external)}")
    
    # Verify aggregation logic
    print("\nAggregation Verification:")
    for flow_type, flows in [
        ("Strasbourg Internal", strasbourg_internal),
        ("External → Strasbourg", external_to_strasbourg), 
        ("Strasbourg → External", strasbourg_to_external)
    ]:
        if flows:
            example = flows[0]
            print(f"{flow_type}: {example[0]} → {example[1]} → {example[2]} → {example[3]}")

if __name__ == "__main__":
    test_csv_processing()
